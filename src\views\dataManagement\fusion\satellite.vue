<template>
  <div class="gnss-monitor-container">
    <el-card class="main-card">
      <el-form :model="queryForm" inline class="filter-container">
        <el-form-item label="时间范围">
          <el-date-picker
            v-model="queryForm.dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            value-format="YYYY-MM-DD"
            :disabled-date="disabledDate"
            :shortcuts="dateShortcuts"
          />
        </el-form-item>
        <el-form-item label="导航系统">
          <el-select
            v-model="queryForm.navigationSystemCode"
            placeholder="请选择系统"
            @change="handleSystemChange"
            style="width: 200px"
          >
            <el-option
              v-for="item in systemOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="卫星PRN">
          <el-select
            style="width: 180px"
            v-model="queryForm.satellitePrn"
            placeholder="请选择PRN"
          >
            <el-option label="全部" value="" />
            <el-option
              v-for="item in prnOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" :icon="Search" @click="handleSearch"
            >搜索</el-button
          >
        </el-form-item>
      </el-form>

      <div class="tables-container">
        <el-card class="table-card" style="flex: 1.6">
          <template #header>
            <div class="card-header">
              <span>融合卫星伪码授时偏差</span>
            </div>
          </template>
          <el-table
            :data="timeOffsetData"
            border
            stripe
            class="data-table"
            v-loading="loading"
            :header-cell-style="headerCellStyle"
            :cell-style="cellStyle"
          >
            <el-table-column prop="timestamp" label="UTC时间" width="200"/>
            <el-table-column prop="satellitePrn" label="卫星" />
            <el-table-column prop="frequencyId" label="频点标识" />

            <!-- <el-table-column prop="navigationSystemCode" label="导航系统编码" >
              <template #default="scope">
                {{ getSystemOptionsName(scope.row.navigationSystemCode) }}
              </template>
            </el-table-column> -->
            <el-table-column prop="frequencyBias" label="授时偏差(ns)" />
          </el-table>
          <div class="pagination-container">
            <el-pagination
              v-model:current-page="currentPage1"
              v-model:page-size="pageSize1"
              :page-sizes="[10, 15, 20, 30, 50]"
              layout="sizes, total, prev, pager, next, jumper"
              :total="totalTimeOffset"
              pager-count="4"
              @current-change="handleCurrentChange1"
              @size-change="handleSizeChange1"
            />
          </div>
        </el-card>

        <el-card class="table-card" style="flex: 2">
          <template #header>
            <div class="card-header">
              <span>模型参数</span>
            </div>
          </template>
          <el-table
            :data="forecastModelData"
            border
            stripe
            class="data-table"
            v-loading="loading"
            :header-cell-style="headerCellStyle"
            :cell-style="cellStyle"
          >
            <el-table-column prop="timestamp" label="TOC(UTC)" width="180" />
            <el-table-column prop="generateTime" label="数据生成时间" width="180" />
            <el-table-column prop="satellitePrn" label="卫星" />
            <el-table-column prop="frequencyId" label="频点标识" />
            <el-table-column prop="a0" label="A0(1E-10s)" />
            <el-table-column prop="a1" label="A1(1E-16s/s)" />
            <el-table-column prop="rmse" label="RMS(1E-10s)" />
            <el-table-column prop="checkState" label="状态" >

            </el-table-column>

            <!-- <el-table-column prop="a2" label="A2(1E-21 s/s²)" /> -->
          </el-table>
          <div class="pagination-container">
            <el-pagination
              v-model:current-page="currentPage2"
              v-model:page-size="pageSize2"
              :page-sizes="[10, 15, 20, 30, 50]"
              layout="sizes, total, prev, pager, next, jumper"
              :total="totalForecastModel"
              @current-change="handleCurrentChange2"
              @size-change="handleSizeChange2"
            />
          </div>
        </el-card>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, onMounted, computed } from "vue";
import { Search } from "@element-plus/icons-vue";
import apiAjax from "@/api/index";
import { useAllSatellite } from "@/store/modules/allSatellite";
const allSatelliteStore = useAllSatellite();
// 修改为表单数据
const queryForm = ref({
  dateRange: null,
  navigationSystemCode: "",
  satellitePrn: "",
});
// 导航系统选项
const systemOptions = computed(() => {
  if (!allSatelliteStore.data || !allSatelliteStore.data.length) return [];
  return allSatelliteStore.data.map((system) => ({
    value: system.systemCode,
    label: system.systemName,
  }));
});
const prnOptions = computed(() => {
  if (!queryForm.value.navigationSystemCode || !allSatelliteStore.data)
    return [];

  // 找到选中的导航系统
  const selectedSystem = allSatelliteStore.data.find(
    (system) => system.systemCode === queryForm.value.navigationSystemCode,
  );

  if (!selectedSystem || !selectedSystem.satelliteInfos) return [];

  // 返回该系统下的所有卫星PRN
  return selectedSystem.satelliteInfos.map((satellite) => ({
    value: satellite.prn,
    label: satellite.prn,
  }));
});

const getSystemOptionsName = (value) => {
  let item = systemOptions.value.find((i) => i.value == value);
  return item.label;
};

// 表头样式
const headerCellStyle = {
  background: "#f5f7fa",
  color: "#606266",
  fontWeight: "bold",
  fontSize: "17px",
  height: "50px",
};

// 单元格样式
const cellStyle = {
  fontSize: "16px",
  padding: "10px 0",
};

const loading = ref(false);

// 日期快捷选项
const dateShortcuts = [
  {
    text: "最近一周",
    value: () => {
      const end = new Date();
      const start = new Date();
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
      return [start, end];
    },
  },
  {
    text: "最近一个月",
    value: () => {
      const end = new Date();
      const start = new Date();
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
      return [start, end];
    },
  },
  {
    text: "最近三个月",
    value: () => {
      const end = new Date();
      const start = new Date();
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
      return [start, end];
    },
  },
];

// 禁用日期 - 禁用未来日期
const disabledDate = (time) => {
  return time.getTime() > Date.now();
};

// 修改搜索处理函数
const handleSearch = () => {
  currentPage1.value = 1;
  currentPage2.value = 1;
  loadTimeOffsetData();
  loadForecastModelData();
};

// 页面大小 - 分别为两个表格设置独立的页面大小
const pageSize1 = ref(15);
const pageSize2 = ref(15);

// 系统时差数据相关
const timeOffsetData = ref([]);
const totalTimeOffset = ref(0);
const currentPage1 = ref(1);

// 预报导航系统相关
const forecastModelData = ref([]);
const totalForecastModel = ref(0);
const currentPage2 = ref(1);

// 分页切换处理
const handleCurrentChange1 = (val) => {
  currentPage1.value = val;
  loadTimeOffsetData();
};

const handleCurrentChange2 = (val) => {
  currentPage2.value = val;
  loadForecastModelData();
};

// 页面大小变化处理
const handleSizeChange1 = (val) => {
  pageSize1.value = val;
  currentPage1.value = 1;
  loadTimeOffsetData();
};

const handleSizeChange2 = (val) => {
  pageSize2.value = val;
  currentPage2.value = 1;
  loadForecastModelData();
};

// 构建查询参数
const buildQueryParams = (page, size) => {
  const params = new URLSearchParams();

  // 时间范围参数
  if (queryForm.value.dateRange?.length === 2) {
    params.append("beginTime", `${queryForm.value.dateRange[0]} 00:00:00`);
    params.append("endTime", `${queryForm.value.dateRange[1]} 23:59:59`);
  }

  // 导航系统
  if (
    queryForm.value.navigationSystemCode &&
    queryForm.value.navigationSystemCode !== "all"
  ) {
    params.append("navigationSystemCode", queryForm.value.navigationSystemCode);
  }
  if (queryForm.value.satellitePrn && queryForm.value.satellitePrn !== "all") {
    params.append("satellitePrn", queryForm.value.satellitePrn);
  }

  // 分页参数
  params.append("page", page);
  params.append("size", size);

  return params.toString();
};

const handleSystemChange = () => {
  queryForm.value.satellitePrn = "";
};

// 修改数据加载函数
const loadTimeOffsetData = async () => {
  loading.value = true;
  try {
    const queryString = buildQueryParams(currentPage1.value, pageSize1.value);
    const { data: response } = await apiAjax.get(
      `/api/jnx/fusion/apps/satellite/fusion/timeDiff/wxwmsspc/findTimeDiffDataPage?${queryString}`,
    );

    if (response) {
      timeOffsetData.value = response.content || [];
      totalTimeOffset.value = response.totalElements || 0;
    }
  } catch (error) {
    console.error("获取时差数据失败:", error);
  } finally {
    loading.value = false;
  }
};

const loadForecastModelData = async () => {
  loading.value = true;
  try {
    const queryString = buildQueryParams(currentPage2.value, pageSize2.value);
    const { data: response } = await apiAjax.get(
      `/api/jnx/fusion/apps/satellite/fusion/timeDiff/wxwmsspc/findTimeDiffModelParamPage?${queryString}`,
    );

    if (response) {
      forecastModelData.value = response.content || [];
      totalForecastModel.value = response.totalElements || 0;
    }
  } catch (error) {
    console.error("获取预报模型数据失败:", error);
  } finally {
    loading.value = false;
  }
};
// 生命周期钩子
onMounted(async () => {
  await allSatelliteStore.initAllData();
  queryForm.value.navigationSystemCode = allSatelliteStore.data[0].systemCode;
  loadTimeOffsetData();
  loadForecastModelData();
});
</script>

<style scoped lang="scss">
.gnss-monitor-container {
  // padding-top: 16px;
  height: 85vh;
  overflow: hidden;
  background: #ffffff;
  .page-header {
    h3 {
      font-size: 1.5rem;
      color: #303133;
      margin: 0;
      padding-bottom: 10px;
      border-bottom: 1px solid #ebeef5;
    }
  }

  .main-card {
    // margin-bottom: 20px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  }

  .filter-container {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    gap: 16px;
    margin-bottom: 16px;

    :deep(.el-form-item) {
      margin-bottom: 0;
      margin-right: 16px;
    }

    :deep(.el-form-item__label) {
      font-weight: bold;
    }
  }

  .tables-container {
    display: flex;
    flex-wrap: wrap;
    gap: 20px;
    justify-content: space-between;

    .table-card {
      flex: 1;
      min-width: 600px;
      margin-bottom: 20px;

      .card-header {
        display: flex;
        justify-content: space-between;
        align-items: center;

        span {
          font-size: 16px;
          font-weight: bold;
          color: #409eff;
        }
      }

      .data-table {
        width: 100%;
      }

      .pagination-container {
        margin-top: 16px;
        display: flex;
        justify-content: flex-end;
        padding: 10px 0;
      }
    }
  }
}

// 响应式布局调整
@media screen and (max-width: 1400px) {
  .gnss-monitor-container {
    .tables-container {
      flex-direction: column;

      .table-card {
        width: 100%;
      }
    }
  }
}

:deep(.el-table) {
  &.el-table--striped .el-table__body tr.el-table__row--striped td {
    background-color: #fafafa;
  }

  .el-table__header th {
    background-color: #f5f7fa;
    color: #606266;
    font-weight: bold;
  }
}
.data-table {
  height: 60vh;
}
</style>
