/* eslint-disable */
/* prettier-ignore */
// Generated by elegant-router
// Read more: https://github.com/soybeanjs/elegant-router

import type { GeneratedRoute } from '@elegant-router/types';

export const generatedRoutes: any = [
  {
    name: '403',
    path: '/403',
    component: 'layout.blank$view.403',
    meta: {
      title: '403',
      constant: true,
      hideInMenu: true,
      i18nKey: 'route.403'
    }
  },
  {
    name: '404',
    path: '/404',
    component: 'layout.blank$view.404',
    meta: {
      title: '404',
      constant: true,
      hideInMenu: true,
      i18nKey: 'route.404'
    }
  },
  {
    name: '500',
    path: '/500',
    component: 'layout.blank$view.500',
    meta: {
      title: '500',
      constant: true,
      hideInMenu: true,
      i18nKey: 'route.500'
    }
  },
  {
    name: 'alarmlist',
    path: '/alarmlist',
    component: 'layout.base$view.alarmlist',
    meta: {
      title: '异常业务',
      order: 7
    }
  },
  {
    name: 'assessmentmanager',
    path: '/assessmentmanager',
    component: 'layout.base',
    meta: {
      title: '评估管理',
      order: 4
    },
    children: [
      {
        name: 'assessmentmanager_eop',
        path: '/assessmentmanager/eop',
        component: 'view.assessmentmanager_eop',
        meta: {
          title: 'EOP评估',
          order: 1
        }
      },
      {
        name: 'assessmentmanager_ggto',
        path: '/assessmentmanager/ggto',
        component: 'view.assessmentmanager_ggto',
        meta: {
          title: 'GGTO评估',
          order: 2
        }
      },
      {
        name: 'assessmentmanager_timingperformanceevaluation',
        path: '/assessmentmanager/timingperformanceevaluation',
        component: 'view.assessmentmanager_timingperformanceevaluation',
        meta: {
          title: '授时性能评估'
        }
      },
      {
        name: 'assessmentmanager_utco',
        path: '/assessmentmanager/utco',
        component: 'view.assessmentmanager_utco',
        meta: {
          title: 'UTCO评估',
          order: 3
        }
      }
    ]
  },
  {
    name: 'datamanagement',
    path: '/datamanagement',
    component: 'layout.base',
    meta: {
      title: '数据管理',
      order: 5
    },
    children: [
      {
        name: 'datamanagement_accuracyclass',
        path: '/datamanagement/accuracyclass',
        component: 'view.datamanagement_accuracyclass',
        meta: {
          title: 'datamanagement_accuracyclass'
        }
      },
      {
        name: 'datamanagement_fusion',
        path: '/datamanagement/fusion',
        component: 'view.datamanagement_fusion',
        meta: {
          title: '融合数据'
        }
      },
      {
        name: 'datamanagement_gnssmonitor',
        path: '/datamanagement/gnssmonitor',
        component: 'view.datamanagement_gnssmonitor',
        meta: {
          title: 'GNSS平台系统时差'
        }
      },
      {
        name: 'datamanagement_navigation',
        path: '/datamanagement/navigation',
        component: 'view.datamanagement_navigation',
        meta: {
          title: ' 原始时差数据'
        }
      },
      {
        name: 'datamanagement_oneminute',
        path: '/datamanagement/oneminute',
        component: 'view.datamanagement_oneminute',
        meta: {
          title: '1分钟降频时差数据'
        }
      },
      {
        name: 'datamanagement_platform1603',
        path: '/datamanagement/platform1603',
        component: 'view.datamanagement_platform1603',
        meta: {
          title: '1603平台授时偏差'
        }
      },
      {
        name: 'datamanagement_rawtimediff',
        path: '/datamanagement/rawtimediff',
        meta: {
          title: '导航电文'
        },
        children: [
          {
            name: 'datamanagement_rawtimediff_b2b',
            path: '/datamanagement/rawtimediff/b2b',
            component: 'view.datamanagement_rawtimediff_b2b',
            meta: {
              title: 'B2b改正数'
            }
          },
          {
            name: 'datamanagement_rawtimediff_bds',
            path: '/datamanagement/rawtimediff/bds',
            component: 'view.datamanagement_rawtimediff_bds',
            meta: {
              title: '电离层模型参数'
            }
          },
          {
            name: 'datamanagement_rawtimediff_eop',
            path: '/datamanagement/rawtimediff/eop',
            component: 'view.datamanagement_rawtimediff_eop',
            meta: {
              title: 'EOP'
            }
          },
          {
            name: 'datamanagement_rawtimediff_ggto',
            path: '/datamanagement/rawtimediff/ggto',
            component: 'view.datamanagement_rawtimediff_ggto',
            meta: {
              title: 'GGTO'
            }
          },
          {
            name: 'datamanagement_rawtimediff_utco',
            path: '/datamanagement/rawtimediff/utco',
            component: 'view.datamanagement_rawtimediff_utco',
            meta: {
              title: 'UTCO'
            }
          }
        ]
      },
      {
        name: 'datamanagement_transceiver',
        path: '/datamanagement/transceiver',
        component: 'view.datamanagement_transceiver',
        meta: {
          title: '收发数据'
        }
      }
    ]
  },
  {
    name: 'filemanagement',
    path: '/filemanagement',
    component: 'layout.base$view.filemanagement',
    meta: {
      title: '文件管理',
      order: 6
    }
  },
  {
    name: 'help',
    path: '/help',
    component: 'layout.base$view.help',
    meta: {
      title: '帮助',
      order: 10
    }
  },
  {
    name: 'home',
    path: '/home',
    component: 'layout.base$view.home',
    meta: {
      title: '首页',
      i18nKey: 'route.home',
      order: 1
    }
  },
  {
    name: 'interfacestate',
    path: '/interfacestate',
    component: 'layout.base$view.interfacestate',
    meta: {
      title: '接口状态',
      order: 7
    }
  },
  {
    name: 'log',
    path: '/log',
    component: 'layout.base$view.log',
    meta: {
      title: '日志',
      order: 9
    }
  },
  {
    name: 'login',
    path: '/login/:module(pwd-login|code-login|register|reset-pwd|bind-wechat)?',
    component: 'layout.blank$view.login',
    props: true,
    meta: {
      title: 'login',
      constant: true,
      hideInMenu: true
    }
  },
  {
    name: 'operatingcondition',
    path: '/operatingcondition',
    component: 'layout.base$view.operatingcondition',
    meta: {
      title: '系统工况',
      order: 8
    }
  },
  {
    name: 'params-manage',
    path: '/params-manage',
    component: 'layout.base$view.params-manage',
    meta: {
      title: '参数管理',
      order: 2
    }
  }
];
