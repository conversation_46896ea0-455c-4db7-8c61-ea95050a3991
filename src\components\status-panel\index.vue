<script setup lang="ts">
import { ref, watch } from "vue";

interface TimingData {
  system: string;
  value: number;
}

interface AlertMessage {
  id: number;
  message: string;
}

interface Props {
  timingData: TimingData[];
  alerts: AlertMessage[];
}

const props = defineProps<Props>();
const displayAlerts = ref<AlertMessage[]>([]);

// 监听新的告警消息
watch(
  () => props.alerts,
  (newAlerts) => {
    if (newAlerts.length > 0) {
      // 只保留最新的3条消息，并反转顺序使最新的在上面
      displayAlerts.value = [...newAlerts].slice(-4).reverse();
    }
  },
  { deep: true, immediate: true },
);
</script>

<template>
  <div class="status-wrapper">
    <div class="content-wrapper">
      <div class="timing-grid">
        <div class="panel-title">星地融合授时偏差</div>
        <div v-for="item in timingData" :key="item.system" class="timing-item">
          <div class="timing-content">
            <div class="timing-left">
              <span class="system-icon"></span>
              <span class="system-name">{{ item.system }}</span>
            </div>
            <div class="timing-right">
              <span class="value">{{ item.value }}</span>
              <span class="unit">ns</span>
            </div>
          </div>
        </div>
      </div>
      <div class="alert-wrapper">
        <!-- <div class="alert-header">实时告警</div> -->
        <TransitionGroup name="alert" tag="div" class="alert-content">
          <div
            v-for="alert in displayAlerts"
            :key="alert.id"
            class="alert-item"
          >
            <div class="alert-icon">●</div>
            <div class="alert-message">{{ alert.message }}</div>
          </div>
        </TransitionGroup>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.status-wrapper {
  margin-bottom: 8px;
}

.content-wrapper {
  display: flex;
  gap: 8px;
  height: 110px;
}

.panel-title {
  font-size: 15px;
  font-weight: 500;
  color: #1e293b;
  padding: 8px 20px;
  border-bottom: 1px solid rgba(14, 165, 233, 0.1);
  grid-column: 1 / -1;
}

.timing-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  grid-template-rows: auto 1fr;
  gap: 1px;
  padding: 6px;
  background: #ffffff;
  border-radius: 4px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  flex: 1;
  border: 1px solid rgba(14, 165, 233, 0.1);
}

.timing-item {
  display: flex;
  align-items: center;
  // padding: 8px 20px;
  background: #ffffff;
  border-radius: 2px;
  min-width: 130px;
  position: relative;

  &:hover {
    background: rgba(14, 165, 233, 0.03);
  }

  &::after {
    content: "";
    position: absolute;
    right: 0;
    top: 20%;
    height: 60%;
    width: 1px;
    background: rgba(14, 165, 233, 0.1);
  }

  &:last-child::after {
    display: none;
  }
}

.timing-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: space-between;
  width: 100%;
}

.timing-left {
  display: flex;
  align-items: center;
  gap: 8px;
  min-width: 80px;
}

.timing-right {
  display: flex;
  align-items: baseline;
  gap: 2px;
  min-width: 60px;
}

.system-icon {
  width: 7px;
  height: 7px;
  border-radius: 50%;
  background: #0ea5e9;
  opacity: 0.7;
}

.system-name {
  font-size: 14px;
  color: #64748b;
  font-weight: 500;
  letter-spacing: 0.5px;
}

.value {
  font-size: 24px;
  font-weight: 600;
  color: #0ea5e9;
}

.unit {
  font-size: 12px;
  color: #94a3b8;
  font-weight: normal;
  margin-left: 2px;
}

.alert-wrapper {
  flex: 1;
  background: #ffffff;
  border-radius: 4px;
  padding: 6px;
  overflow: hidden;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  border: 1px solid rgba(14, 165, 233, 0.1);
  display: flex;
  flex-direction: column;
}

.alert-header {
  font-size: 11px;
  color: #64748b;
  padding: 0 4px 4px;
  border-bottom: 1px solid rgba(14, 165, 233, 0.1);
  font-weight: 500;
}

.alert-content {
  display: flex;
  flex-direction: column;
  gap: 2px;
  position: relative;
  padding-top: 4px;
  flex: 1;
}

.alert-item {
  font-size: 13px;
  color: #0ea5e9;
  white-space: nowrap;
  line-height: 1.2;
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 2px 6px;

  &:hover {
    background: rgba(14, 165, 233, 0.03);
    border-radius: 2px;
  }
}

.alert-icon {
  font-size: 9px;
  color: #0ea5e9;
  animation: blink 2s infinite;
}

.alert-message {
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
}

@keyframes blink {
  0%,
  100% {
    opacity: 1;
  }
  50% {
    opacity: 0.3;
  }
}

// 告警消息的进入和离开动画
.alert-enter-active,
.alert-leave-active {
  transition: all 0.3s ease;
}

.alert-enter-from {
  opacity: 0;
  transform: translateY(-20px);
}

.alert-leave-to {
  opacity: 0;
  transform: translateY(20px);
}

.alert-move {
  transition: transform 0.3s ease;
}
</style>
