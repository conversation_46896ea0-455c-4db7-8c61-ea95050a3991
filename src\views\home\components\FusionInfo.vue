<template>
  <div class="fusion-info">
    <div class="chart-content">
      <line-chart :data="lineChartOption" />
    </div>
    <div class="tables-section">
      <div class="table-wrapper">
        <div class="table-title">星地融合授时偏差</div>
        <el-table
          :data="
            timeDiffList.slice(
              (timeDiffCurrentPage - 1) * timeDiffPageSize,
              timeDiffCurrentPage * timeDiffPageSize,
            )
          "
          border
          style="width: 100%"
          height="250"
        >
          <el-table-column prop="generateTime" label="UTC-时间" width="180" />
          <el-table-column prop="BDS" label="UTC-BDS (ns)" />
          <el-table-column prop="GPS" label="UTC-GPS (ns)" />
          <el-table-column prop="GLONASS" label="UTC-GLONASS (ns)" />
          <el-table-column prop="GALILEO" label="UTC-GALILEO (ns)" />
        </el-table>
      </div>
      <div class="table-wrapper">
        <div class="table-title">星地融合授时偏差模型参数</div>
        <el-table
          :data="
            modelParamsList.slice(
              (modelParamsCurrentPage - 1) * modelParamsPageSize,
              modelParamsCurrentPage * modelParamsPageSize,
            )
          "
          border
          style="width: 100%"
          height="250"
        >
          <el-table-column prop="generateTime" label="参数成时间(UTC)" width="180" />
          <el-table-column prop="system" label="时差类型"  />
          <el-table-column prop="TOC" label="TOC" width="100" />
          <el-table-column prop="A0" label="A0(1E-10s)" />
          <el-table-column prop="A1" label="A1(1E-16s/s)" />
          <el-table-column prop="A2" label="A2(1E-21 s/s²)" />
        </el-table>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from "vue";
import LineChart from "@/components/lineChart/lineChart.vue";

// 星地融合授时偏差分页相关
const timeDiffCurrentPage = ref(1);
const timeDiffPageSize = ref(10);


// 星地融合授时偏差模型参数分页相关
const modelParamsCurrentPage = ref(1);
const modelParamsPageSize = ref(10);


const activeTab = ref("timeList");

// 生成图表数据
const lineChartOption = ref({
  xAxis: {
    type: "category",
    data: Array.from(
      { length: 24 },
      (_, i) => `${String(i).padStart(2, "0")}:00`,
    ),
  },
  yAxis: {
    type: "value",
    name: "时差(ns)",
  },
  legend: {
    bottom: 10,
    data: ["UTC(NTSC)-BDT", "UTC(NTSC)-GPST", "UTC(NTSC)-GLNT", "UTC(NTSC)-GST"],
  },
  grid: {
    top: "30px",
    left: "50px",
    right: "30px",
    bottom: "60px",
  },
  tooltip: {
    trigger: "axis",
    formatter: function(params) {
      const now = new Date();
      const hour = parseInt(params[0].name);
      const time = new Date(now.getFullYear(), now.getMonth(), now.getDate(), hour, 0, 0);
      const formattedTime = time.toLocaleString("zh-CN", {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit',
        hour12: false
      }).replace(/\//g, '-');

      let result = `${formattedTime}<br/>`;
      params.forEach(param => {
        result += `${param.seriesName}: ${param.value} ns<br/>`;
      });
      return result;
    }
  },
  series: [
    {
      name: "UTC(NTSC)-BDT",
      data: Array.from({ length: 24 }, () =>
        (Math.random() * 200 - 100).toFixed(1),
      ),
      type: "line",
      smooth: true,
      showSymbol: true,
      symbolSize: 6,
      itemStyle: {
        color: "#3B8CFF",
        borderWidth: 2,
        borderColor: "#fff"
      },
      lineStyle: {
        width: 2,
        shadowColor: "rgba(59,140,255,0.2)",
        shadowBlur: 10
      }
    },
    {
      name: "UTC(NTSC)-GPST",
      data: Array.from({ length: 24 }, () =>
        (Math.random() * 200 - 100).toFixed(1),
      ),
      type: "line",
      smooth: true,
      showSymbol: true,
      symbolSize: 6,
      itemStyle: {
        color: "#FFA07A",
        borderWidth: 2,
        borderColor: "#fff"
      },
      lineStyle: {
        width: 2,
        shadowColor: "rgba(255,160,122,0.2)",
        shadowBlur: 10
      }
    },
    {
      name: "UTC(NTSC)-GLNT",
      data: Array.from({ length: 24 }, () =>
        (Math.random() * 200 - 100).toFixed(1),
      ),
      type: "line",
      smooth: true,
      showSymbol: true,
      symbolSize: 6,
      itemStyle: {
        color: "#4CAF50",
        borderWidth: 2,
        borderColor: "#fff"
      },
      lineStyle: {
        width: 2,
        shadowColor: "rgba(76,175,80,0.2)",
        shadowBlur: 10
      }
    },
    {
      name: "UTC(NTSC)-GST",
      data: Array.from({ length: 24 }, () =>
        (Math.random() * 200 - 100).toFixed(1),
      ),
      type: "line",
      smooth: true,
      showSymbol: true,
      symbolSize: 6,
      itemStyle: {
        color: "#FF6347",
        borderWidth: 2,
        borderColor: "#fff"
      },
      lineStyle: {
        width: 2,
        shadowColor: "rgba(255,99,71,0.2)",
        shadowBlur: 10
      }
    }
  ],
});

// 生成星地融合授时偏差数据
const timeDiffList = ref([]);
const generateTimeDiffList = () => {
  const now = new Date();
  timeDiffList.value = Array.from({ length: 100 }, (_, i) => {
    const time = new Date(now - i * 60000);
    return {
      generateTime: time.toLocaleString("zh-CN", {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit',
        hour12: false
      }).replace(/\//g, '-'),
      BDS: (Math.random() * 200 - 100).toFixed(2),
      GPS: (Math.random() * 200 - 100).toFixed(2),
      GLONASS: (Math.random() * 200 - 100).toFixed(2),
      GALILEO: (Math.random() * 200 - 100).toFixed(2),
    };
  });
};

// 生成星地融合授时偏差模型参数数据
const modelParamsList = ref([]);
const generateModelParams = () => {
  const systems = ["UTC(NTSC)-BDT", "UTC(NTSC)-GPST", "UTC(NTSC)-GLNT", "UTC(NTSC)-GST"];
  const now = new Date();
  modelParamsList.value = systems.flatMap((system) =>
    Array.from({ length: 25 }, (_, i) => {
      const time = new Date(now - i * 3600000);
      return {
        generateTime: time.toLocaleString("zh-CN", {
          year: 'numeric',
          month: '2-digit',
          day: '2-digit',
          hour: '2-digit',
          minute: '2-digit',
          second: '2-digit',
          hour12: false
        }).replace(/\//g, '-'),
        system,
        TOC: 331200,
        A0: (Math.random() * 100).toFixed(0),
        A1: (Math.random() * 100).toFixed(0),
        A2: (Math.random() * 100).toFixed(0),
      };
    }),
  );
};

onMounted(() => {
  generateTimeDiffList();
  generateModelParams();
  // 每分钟更新星地融合授时偏差
  setInterval(generateTimeDiffList, 60000);
  // 每小时更新星地融合授时偏差模型参数
  setInterval(generateModelParams, 3600000);
});
</script>

<style lang="scss" scoped>
.fusion-info {
  .chart-content {
    height: 420px;
    margin-bottom: 20px;
  }

  .tables-section {
    display: flex;
    gap: 20px;
    margin-top: 20px;

    .table-wrapper {
      flex: 1;
      background-color: #fff;
      border-radius: 4px;
      padding: 15px;
      box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);

      .table-title {
        font-size: 16px;
        font-weight: 500;
        margin-bottom: 15px;
        color: #303133;
        padding-left: 5px;
        border-left: 4px solid #409eff;
      }
    }
  }
}
</style>
