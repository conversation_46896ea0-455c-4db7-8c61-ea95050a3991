<template>
  <div class="con">
    <v-chart
      ref="chartRef"
      autoresize
      class="chart"
      :option="props.data || {}"
    ></v-chart>
  </div>
</template>

<script setup>
import { onBeforeUnmount, onMounted, ref } from "vue";
const props = defineProps({
  data: {
    type: Object,
    required: false,
    default: {},
  },
  objectSpanMethod: {
    type: Function,
    required: false,
    default: null,
  },
});

const chartRef = ref(null);
let chartInstance = null;
const initAllListeners = () => {
  chartInstance.on("legendselectchanged", (event) => {
    const selectedData = event.selected;
    const tableData = props.data.tableData || [];
    console.log(selectedData);
    for (let key in selectedData) {
      const selected = selectedData[key];
      const tableItemList = tableData.filter((item) => item.name === key);
      if (tableItemList.length) {
        tableItemList.forEach((tableItem) => {
          tableItem.show = !selected;
        });
      }
    }
  });

  window.addEventListener("resize", resize);
};

const removeAllListeners = () => {
  if (chartInstance) chartInstance.off("legendselectchanged");
  window.removeEventListener("resize", resize);
};

const resize = () => {
  if (chartInstance) chartInstance.resize();
};

const getIns = () => {
  return chartInstance;
};

const clear = () => {
  removeAllListeners();
  if (chartInstance && !chartInstance.isDisposed) {
    chartInstance.dispose();
    chartInstance = null;
  }
};

// 显示暴露的数据，才可以在父组件拿到
defineExpose({
  clear,
  resize,
  getIns,
  initAllListeners,
});

onMounted(() => {
  chartInstance = chartRef.value.chart;
  initAllListeners();
});

onBeforeUnmount(() => {
  clear();
});
</script>

<style lang="scss" scoped>
.con {
  width: 100%;
  height: 100%;
}
.chart {
  width: 100%;
  height: 100%;
}
:global(.el-table .hidden-row) {
  display: none;
}
</style>
