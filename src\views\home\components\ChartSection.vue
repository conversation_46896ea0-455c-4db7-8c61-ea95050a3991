<template>
  <div class="chart-section">
    <div class="chart-container">
      <el-tabs v-model="activeTab">
        <el-tab-pane label="星地融合授时信息" name="fusion">
          <fusion-info />
        </el-tab-pane>
        <el-tab-pane label="卫星伪码授时增强信息" name="pseudoRange">
          <pseudo-range-info />
        </el-tab-pane>
        <!-- <el-tab-pane label="系统伪码时差" name="systemPseudo">
          <system-pseudo-time-diff />
        </el-tab-pane>
        <el-tab-pane label="系统载波相位授时偏差" name="carrierPhase">
          <carrier-phase-bias />
        </el-tab-pane>
        <el-tab-pane label="系统载波相位时差" name="carrierPhaseDiff">
          <carrier-phase-diff />
        </el-tab-pane> -->
        <!-- <el-tab-pane label="B2b PPP时差" name="b2bPPP">
          <B2bPPPDiff />
        </el-tab-pane> -->
        <el-tab-pane label="授时精度" name="accuracy">
          <satellite-accuracy />
        </el-tab-pane>
      </el-tabs>
    </div>
  </div>
</template>

<script setup>
import { ref } from "vue";
import FusionInfo from './FusionInfo.vue';
import PseudoRangeInfo from './PseudoRangeInfo.vue';
import SystemPseudoTimeDiff from './SystemPseudoTimeDiff.vue';
import CarrierPhaseBias from './CarrierPhaseBias.vue';
import CarrierPhaseDiff from './CarrierPhaseDiff.vue';
import B2bPPPDiff from './B2bPPPDiff.vue';
import SatelliteAccuracy from './SatelliteAccuracy.vue';

const activeTab = ref("fusion");
</script>

<style lang="scss" scoped>
.chart-section {
  // padding: 20px;

  .chart-container {
    background: #fff;
    border-radius: 4px;
    padding: 20px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  }
}
</style>
