<template>
  <div>
    <StatusPanel :timing-data="timingData" :alerts="alerts" />
    <div class="home-container">
    <chart-section />
  </div>
  </div>

</template>

<script setup>
import StatusPanel from "@/components/status-panel/index.vue";
import ChartSection from './components/ChartSection.vue';
import { ref } from 'vue';
const timingData = ref([
  { system: 'UTC(NTSC)-BDT', value: 200 },
  { system: 'UTC(NTSC)-GPST', value: 200 },
  { system: 'UTC(NTSC)-GLNT', value: 200 },
  { system: 'UTC(NTSC)-GST', value: 200 }
]);
const alerts = ref([
  {id: 1, message: '2025-3-12 10:21:22 接收机1运行异常'},
  {id: 2, message: '2025-3-12 16:21:22 卫星C09 SISRE超限'},
  {id: 3, message: '2025-3-12 22:00:00 星地融合授时信息生成失败'},
  {id: 4, message: '2025-3-12 23:01:00 卫星G02 SISRE超限'},
]);
let alertId = 5;
// 模拟添加新告警
setInterval(() => {
  const newAlert = {
    id: alertId++,
    message: `2025-3-12 23:01:00 卫星G02 SISRE超限`
  };
  // alerts.value.push(newAlert);
  // 只保留最新的3条消息
  if (alerts.value.length > 4) {
    alerts.value.shift();
  }
}, 1000); // 每秒添加一条新消息

</script>

<style lang="scss" scoped>
.home-container {
  background: #f5f7fa;
}

:deep(.el-tabs__header) {
  margin-bottom: 20px;
}

:deep(.el-table) {
  --el-table-border-color: #EBEEF5;
  --el-table-header-bg-color: #F5F7FA;
}
</style>
